<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbeidsavtale PDF Generator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .instructions h2 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #2196f3;
        }
        
        .button {
            background: #2196f3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .button:hover {
            background: #1976d2;
        }
        
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .file-info {
            background: #fff3e0;
            border: 1px solid #ff9800;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        @media print {
            body {
                background: white;
            }
            .instructions, .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Arbeidsavtale PDF Generator</h1>
        
        <div class="instructions no-print">
            <h2>📄 Slik lager du PDF av arbeidsavtalen:</h2>
            
            <div class="step">
                <strong>1. Åpne kontrakten:</strong>
                <a href="arbeidskontrakt.html" class="button" target="_blank">📄 Åpne Arbeidskontrakt</a>
            </div>
            
            <div class="step">
                <strong>2. Skriv ut til PDF:</strong>
                <ul>
                    <li>Trykk <kbd>Ctrl+P</kbd> (eller <kbd>Cmd+P</kbd> på Mac)</li>
                    <li>Velg <strong>"Lagre som PDF"</strong> som skriver</li>
                    <li>Kontroller at innstillingene er riktige (A4, marger)</li>
                    <li>Klikk <strong>"Lagre"</strong> og velg filnavn</li>
                </ul>
            </div>
            
            <div class="step">
                <strong>3. Alternativ: Bruk WeasyPrint (avansert):</strong>
                <p>Hvis du har installert WeasyPrint og GTK-biblioteker:</p>
                <code>python lag_pdf.py</code>
            </div>
        </div>
        
        <div class="success">
            ✅ <strong>HTML-filen er generert!</strong><br>
            Kontrakten er klar for konvertering til PDF.
        </div>
        
        <div class="file-info">
            <h3>📁 Genererte filer:</h3>
            <ul>
                <li><strong>arbeidskontrakt.html</strong> - Kontrakten i HTML-format</li>
                <li><strong>kontrakt-stil.css</strong> - Profesjonell styling</li>
                <li><strong>sample-data.json</strong> - Eksempeldata</li>
            </ul>
        </div>
        
        <h2>🎨 Fordeler med denne tilnærmingen:</h2>
        <ul>
            <li>✅ <strong>Profesjonell layout</strong> - CSS gir full designkontroll</li>
            <li>✅ <strong>Moderne typografi</strong> - Inter font og god lesbarhet</li>
            <li>✅ <strong>Print-optimalisert</strong> - Riktige marger og sidestørrelse</li>
            <li>✅ <strong>Fleksibel</strong> - Enkelt å tilpasse design og innhold</li>
            <li>✅ <strong>Vedlikeholdbar</strong> - Bruker standardteknologier</li>
        </ul>
        
        <h2>🔧 Tilpasning:</h2>
        <ul>
            <li><strong>Innhold:</strong> Rediger <code>arbeidskontrakt.md</code></li>
            <li><strong>Data:</strong> Rediger <code>sample-data.json</code> eller lag ny JSON-fil</li>
            <li><strong>Design:</strong> Rediger <code>kontrakt-stil.css</code></li>
            <li><strong>Generering:</strong> Kjør <code>node generate.js</code></li>
        </ul>
        
        <div class="step">
            <strong>🔄 Regenerer kontrakten:</strong>
            <button class="button" onclick="regenerateContract()">🔄 Generer på nytt</button>
        </div>
    </div>
    
    <script>
        function regenerateContract() {
            if (confirm('Dette vil regenerere HTML-filen. Fortsette?')) {
                // I en ekte implementasjon ville vi kalle Node.js-skriptet
                alert('Kjør: node generate.js\n\nDette vil regenerere arbeidskontrakt.html med oppdaterte data.');
            }
        }
        
        // Sjekk om HTML-filen eksisterer
        fetch('arbeidskontrakt.html')
            .then(response => {
                if (response.ok) {
                    document.querySelector('.success').style.display = 'block';
                } else {
                    document.querySelector('.success').innerHTML = 
                        '⚠️ <strong>HTML-filen ikke funnet!</strong><br>Kjør <code>node generate.js</code> først.';
                    document.querySelector('.success').style.background = '#ffebee';
                    document.querySelector('.success').style.borderColor = '#f44336';
                    document.querySelector('.success').style.color = '#c62828';
                }
            })
            .catch(() => {
                document.querySelector('.success').innerHTML = 
                    '⚠️ <strong>HTML-filen ikke funnet!</strong><br>Kjør <code>node generate.js</code> først.';
                document.querySelector('.success').style.background = '#ffebee';
                document.querySelector('.success').style.borderColor = '#f44336';
                document.querySelector('.success').style.color = '#c62828';
            });
    </script>
</body>
</html>
