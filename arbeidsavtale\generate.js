#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { marked } = require('marked');
const Mustache = require('mustache');

// Configuration
const CONFIG = {
  template: 'arbeidskontrakt.md',
  data: 'sample-data.json',
  htmlOutput: 'arbeidskontrakt.html'
};

// CSS styles for the PDF
const CSS_STYLES = `
<style>
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
    background: white;
  }
  
  h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
    font-size: 28px;
    text-align: center;
  }
  
  h2 {
    color: #34495e;
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 20px;
    border-left: 4px solid #3498db;
    padding-left: 15px;
  }
  
  ul {
    list-style-type: none;
    padding-left: 0;
  }
  
  li {
    margin-bottom: 8px;
    padding: 5px 0;
  }
  
  strong {
    color: #2c3e50;
    background-color: #ecf0f1;
    padding: 2px 6px;
    border-radius: 3px;
  }
  
  p {
    margin-bottom: 15px;
    text-align: justify;
  }
  
  .signature-section {
    margin-top: 50px;
    page-break-inside: avoid;
  }
  
  .signature-line {
    border-bottom: 1px solid #bdc3c7;
    margin: 20px 0;
    padding-bottom: 5px;
    min-height: 30px;
  }
  
  @media print {
    body {
      margin: 0;
      padding: 20px;
    }

    h1 {
      page-break-after: avoid;
    }

    h2 {
      page-break-after: avoid;
    }
  }

  .print-instructions {
    background: #e8f4fd;
    border: 1px solid #3498db;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
    font-size: 14px;
  }

  @media print {
    .print-instructions {
      display: none;
    }
  }
</style>
`;

async function loadTemplate(templatePath) {
  try {
    return fs.readFileSync(templatePath, 'utf8');
  } catch (error) {
    console.error(`Error loading template: ${error.message}`);
    process.exit(1);
  }
}

async function loadData(dataPath) {
  try {
    const data = fs.readFileSync(dataPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error loading data: ${error.message}`);
    process.exit(1);
  }
}

async function generateHTML(template, data) {
  // Use Mustache to fill in the template
  const filledTemplate = Mustache.render(template, data);

  // Convert Markdown to HTML
  const htmlContent = marked(filledTemplate);

  // Add print instructions
  const printInstructions = `
    <div class="print-instructions">
      <h3>📄 Hvordan lage PDF:</h3>
      <ol>
        <li>Trykk <strong>Ctrl+P</strong> (eller Cmd+P på Mac) for å åpne utskriftsmenyen</li>
        <li>Velg <strong>"Lagre som PDF"</strong> som skriver</li>
        <li>Juster innstillinger om nødvendig (A4, marger)</li>
        <li>Klikk <strong>"Lagre"</strong> og velg filnavn</li>
      </ol>
      <p><em>Denne boksen vil ikke vises i PDF-en.</em></p>
    </div>
  `;

  // Wrap in a complete HTML document with styles
  return `
<!DOCTYPE html>
<html lang="no">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Arbeidsavtale - ${data.navn}</title>
  ${CSS_STYLES}
</head>
<body>
  ${printInstructions}
  ${htmlContent}
</body>
</html>
  `;
}

async function openInBrowser(htmlPath) {
  const { exec } = require('child_process');
  const fullPath = path.resolve(htmlPath);

  // Try to open in default browser
  let command;
  switch (process.platform) {
    case 'darwin': // macOS
      command = `open "${fullPath}"`;
      break;
    case 'win32': // Windows
      command = `start "" "${fullPath}"`;
      break;
    default: // Linux and others
      command = `xdg-open "${fullPath}"`;
      break;
  }

  exec(command, (error) => {
    if (error) {
      console.log(`💡 Could not auto-open browser. Please open: ${fullPath}`);
    } else {
      console.log(`🌐 Opened in browser: ${fullPath}`);
    }
  });
}

async function main() {
  try {
    console.log('🚀 Starting modern arbeidsavtale generator...');

    // Load template and data
    console.log('📄 Loading template and data...');
    const template = await loadTemplate(CONFIG.template);
    const data = await loadData(CONFIG.data);

    console.log(`📋 Generating contract for: ${data.navn}`);

    // Generate HTML
    const htmlContent = await generateHTML(template, data);

    // Save HTML
    fs.writeFileSync(CONFIG.htmlOutput, htmlContent);
    console.log(`💾 HTML saved: ${CONFIG.htmlOutput}`);

    // Check if we should only generate HTML
    if (process.argv.includes('--html-only')) {
      console.log('✅ HTML generation completed!');
      console.log(`📄 Open ${CONFIG.htmlOutput} in your browser and use Ctrl+P to save as PDF`);
      return;
    }

    // Try to open in browser automatically
    await openInBrowser(CONFIG.htmlOutput);

    console.log('✅ Contract generation completed successfully!');
    console.log(`📄 HTML file: ${CONFIG.htmlOutput}`);
    console.log('💡 Use Ctrl+P in your browser to save as PDF');

  } catch (error) {
    console.error('❌ Error generating contract:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: node generate.js [options]

Options:
  --help, -h      Show this help message
  --data <file>   Specify data file (default: ${CONFIG.data})
  --html-only     Generate HTML only, don't open browser

Examples:
  node generate.js                           # Generate and open in browser
  node generate.js --data employee.json     # Use custom data file
  node generate.js --html-only               # Generate HTML only

To create PDF:
  1. Run the generator
  2. Use Ctrl+P (or Cmd+P) in your browser
  3. Select "Save as PDF"
  `);
  process.exit(0);
}

// Parse command line arguments
const dataIndex = process.argv.indexOf('--data');
if (dataIndex !== -1 && process.argv[dataIndex + 1]) {
  CONFIG.data = process.argv[dataIndex + 1];
}

// Run the generator
if (require.main === module) {
  main();
}

module.exports = { generateHTML, openInBrowser, loadTemplate, loadData };
