#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { marked } = require('marked');
const Mustache = require('mustache');

// Configuration
const CONFIG = {
  template: 'ringerike-kontrakt.md',
  data: 'sample-data.json',
  htmlOutput: 'ringerike-kontrakt.html'
};

// No inline CSS needed - using external stylesheet

async function loadTemplate(templatePath) {
  try {
    return fs.readFileSync(templatePath, 'utf8');
  } catch (error) {
    console.error(`Error loading template: ${error.message}`);
    process.exit(1);
  }
}

async function loadData(dataPath) {
  try {
    const data = fs.readFileSync(dataPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error loading data: ${error.message}`);
    process.exit(1);
  }
}

async function generateHTML(template, data) {
  // Use Mustache to fill in the template
  const filledTemplate = Mustache.render(template, data);

  // Convert Markdown to HTML
  let htmlContent = marked(filledTemplate);

  // Add signature section with proper CSS classes
  const signatureSection = `
    <div class="signatures">
      <div class="date-place">
        Sted/dato: _________________________________
      </div>
      <div class="signature-container">
        <div class="signature-block">
          <div class="signature-line"></div>
          <div class="signature-label">For bedriften</div>
        </div>
        <div class="signature-block">
          <div class="signature-line"></div>
          <div class="signature-label">Arbeidstaker</div>
        </div>
      </div>
    </div>
  `;

  // Replace the simple signature lines in the markdown with styled version
  htmlContent = htmlContent.replace(
    /<h2[^>]*>8\. Annet<\/h2>[\s\S]*?<p>Arbeidstaker:[^<]*<\/p>/,
    '<h2>8. Annet</h2>\n<p>Bedriftens arbeidsreglement som en del av denne avtale og levert arbeidstakeren.</p>\n' + signatureSection
  );

  // Add CSS classes to sections
  htmlContent = htmlContent.replace(
    /<h2>Arbeidsgiver<\/h2>/,
    '<div class="arbeidsgiver"><h2>Arbeidsgiver</h2>'
  );
  htmlContent = htmlContent.replace(
    /<h2>Arbeidstaker<\/h2>/,
    '</div><div class="arbeidstaker"><h2>Arbeidstaker</h2>'
  );
  htmlContent = htmlContent.replace(
    /<h2>Oppstart<\/h2>/,
    '</div><div class="oppstart"><h2>Oppstart</h2>'
  );
  htmlContent = htmlContent.replace(
    /<h2>Lønn<\/h2>/,
    '</div><div class="lonn"><h2>Lønn</h2>'
  );
  htmlContent = htmlContent.replace(
    /<h2>Diverse<\/h2>/,
    '</div><h2>Diverse</h2>'
  );

  // Wrap in a complete HTML document
  return `
<!DOCTYPE html>
<html lang="no">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Arbeidsavtale - ${data.arbeidstaker_navn}</title>
  <link rel="stylesheet" href="ringerike-stil.css">
</head>
<body>
  ${htmlContent}
</body>
</html>
  `;
}

async function openInBrowser(htmlPath) {
  const { exec } = require('child_process');
  const fullPath = path.resolve(htmlPath);

  // Try to open in default browser
  let command;
  switch (process.platform) {
    case 'darwin': // macOS
      command = `open "${fullPath}"`;
      break;
    case 'win32': // Windows
      command = `start "" "${fullPath}"`;
      break;
    default: // Linux and others
      command = `xdg-open "${fullPath}"`;
      break;
  }

  exec(command, (error) => {
    if (error) {
      console.log(`💡 Could not auto-open browser. Please open: ${fullPath}`);
    } else {
      console.log(`🌐 Opened in browser: ${fullPath}`);
    }
  });
}

async function main() {
  try {
    console.log('🚀 Starting modern arbeidsavtale generator...');

    // Load template and data
    console.log('📄 Loading template and data...');
    const template = await loadTemplate(CONFIG.template);
    const data = await loadData(CONFIG.data);

    console.log(`📋 Generating contract for: ${data.navn}`);

    // Generate HTML
    const htmlContent = await generateHTML(template, data);

    // Save HTML
    fs.writeFileSync(CONFIG.htmlOutput, htmlContent);
    console.log(`💾 HTML saved: ${CONFIG.htmlOutput}`);

    console.log('✅ HTML generation completed successfully!');
    console.log(`📄 HTML file: ${CONFIG.htmlOutput}`);
    console.log('');
    console.log('🎯 Next steps:');
    console.log('   1. Run: python lag_pdf.py');
    console.log('   2. Or use: npm run pdf');
    console.log('   3. Or use: npm run generate (does both steps)');
    console.log('');
    console.log('💡 This approach gives you:');
    console.log('   ✅ Professional PDF layout with CSS');
    console.log('   ✅ Full design control');
    console.log('   ✅ High-quality typography');
    console.log('   ✅ Print-ready output');

  } catch (error) {
    console.error('❌ Error generating contract:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🚀 Modern Arbeidsavtale Generator (HTML+CSS+WeasyPrint)

Usage: node generate.js [options]

Options:
  --help, -h      Show this help message
  --data <file>   Specify data file (default: ${CONFIG.data})

Examples:
  node generate.js                           # Generate HTML
  node generate.js --data employee.json     # Use custom data file

Complete workflow:
  1. npm install                             # Install Node.js dependencies
  2. pip install weasyprint markdown2       # Install Python dependencies
  3. node generate.js                       # Generate HTML from template
  4. python lag_pdf.py                      # Generate PDF from HTML+CSS

Or use shortcuts:
  npm run generate                          # Does steps 3+4 automatically
  npm run setup                             # Install Python dependencies
  npm run html                              # Generate HTML only
  npm run pdf                               # Generate PDF only

This approach gives you:
  ✅ Professional PDF layout with CSS
  ✅ Full design control (fonts, colors, layout)
  ✅ High-quality typography
  ✅ Print-ready A4 output with margins
  ✅ Page numbers and headers
  ✅ Modern, maintainable workflow
  `);
  process.exit(0);
}

// Parse command line arguments
const dataIndex = process.argv.indexOf('--data');
if (dataIndex !== -1 && process.argv[dataIndex + 1]) {
  CONFIG.data = process.argv[dataIndex + 1];
}

// Run the generator
if (require.main === module) {
  main();
}

module.exports = { generateHTML, openInBrowser, loadTemplate, loadData };
