<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ringerike Landskap AS - Arbeidsavtale Generator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f8faf6 0%, #e8f5e8 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(45, 80, 22, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4a7c59;
        }
        
        .header h1 {
            color: #2d5016;
            margin: 0;
            font-size: 28px;
        }
        
        .header .subtitle {
            color: #4a7c59;
            font-size: 16px;
            margin-top: 10px;
        }
        
        .instructions {
            background: #f8faf6;
            border: 2px solid #4a7c59;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .instructions h2 {
            margin-top: 0;
            color: #2d5016;
            font-size: 20px;
        }
        
        .step {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-left: 4px solid #4a7c59;
            border-radius: 0 5px 5px 0;
        }
        
        .step-number {
            background: #2d5016;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .button {
            background: #2d5016;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s ease;
        }
        
        .button:hover {
            background: #1a3009;
        }
        
        .button.secondary {
            background: #4a7c59;
        }
        
        .button.secondary:hover {
            background: #3a5c49;
        }
        
        .success {
            background: #e8f5e8;
            border: 2px solid #4a7c59;
            color: #2d5016;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 600;
        }
        
        .file-info {
            background: #fff8f0;
            border: 1px solid #f6ad55;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4a7c59;
        }
        
        .feature h3 {
            color: #2d5016;
            margin-top: 0;
        }
        
        .kbd {
            background: #e2e8f0;
            border: 1px solid #cbd5e0;
            border-radius: 3px;
            padding: 2px 6px;
            font-family: monospace;
            font-size: 14px;
        }
        
        @media print {
            body {
                background: white;
            }
            .instructions, .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌿 Ringerike Landskap AS</h1>
            <div class="subtitle">Arbeidsavtale Generator</div>
        </div>
        
        <div class="instructions no-print">
            <h2>📄 Slik lager du PDF av arbeidsavtalen:</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Åpne kontrakten:</strong>
                <br><br>
                <a href="ringerike-kontrakt.html" class="button" target="_blank">📄 Åpne Arbeidskontrakt</a>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Skriv ut til PDF:</strong>
                <ul style="margin-top: 10px;">
                    <li>Trykk <span class="kbd">Ctrl+P</span> (eller <span class="kbd">Cmd+P</span> på Mac)</li>
                    <li>Velg <strong>"Lagre som PDF"</strong> som skriver</li>
                    <li>Kontroller at innstillingene er riktige (A4, marger)</li>
                    <li>Klikk <strong>"Lagre"</strong> og velg filnavn</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Alternativ: Bruk WeasyPrint (avansert):</strong>
                <p>Hvis du har installert WeasyPrint og GTK-biblioteker:</p>
                <code style="background: #f1f5f9; padding: 5px 10px; border-radius: 3px;">python lag_pdf.py ringerike-kontrakt.html ringerike-stil.css ringerike-kontrakt.pdf</code>
            </div>
        </div>
        
        <div class="success">
            ✅ <strong>Arbeidsavtale for Ringerike Landskap AS er generert!</strong><br>
            Kontrakten er klar for konvertering til PDF med profesjonell layout.
        </div>
        
        <div class="file-info">
            <h3>📁 Genererte filer for Ringerike Landskap AS:</h3>
            <ul>
                <li><strong>ringerike-kontrakt.html</strong> - Kontrakten i HTML-format</li>
                <li><strong>ringerike-stil.css</strong> - Bedriftsspesifikk styling</li>
                <li><strong>sample-data.json</strong> - Eksempeldata for Ole Hansen</li>
                <li><strong>ringerike-kontrakt.md</strong> - Markdown-template</li>
            </ul>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🎨 Bedriftsprofil</h3>
                <p>Tilpasset design med Ringerike Landskap AS sine farger og profil</p>
            </div>
            
            <div class="feature">
                <h3>📋 Komplett kontrakt</h3>
                <p>Alle nødvendige seksjoner for anleggsgartner-stillinger</p>
            </div>
            
            <div class="feature">
                <h3>⚖️ Juridisk korrekt</h3>
                <p>Følger arbeidsmiljøloven og bransjestandarder</p>
            </div>
            
            <div class="feature">
                <h3>🖨️ Print-klar</h3>
                <p>A4-format med riktige marger og profesjonell layout</p>
            </div>
        </div>
        
        <h2>🔧 Tilpasning for nye ansatte:</h2>
        <ol>
            <li><strong>Rediger data:</strong> Oppdater <code>sample-data.json</code> med nye ansattopplysninger</li>
            <li><strong>Regenerer:</strong> Kjør <code>node generate.js</code></li>
            <li><strong>Lag PDF:</strong> Følg instruksjonene ovenfor</li>
        </ol>
        
        <div class="step">
            <strong>🔄 Regenerer kontrakten:</strong>
            <button class="button secondary" onclick="regenerateContract()">🔄 Generer på nytt</button>
            <button class="button" onclick="openDataFile()">📝 Rediger ansattdata</button>
        </div>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e2e8f0; text-align: center; color: #666;">
            <p><strong>Ringerike Landskap AS</strong> | Birchs vei 7, 3530 Røyse | Org.nr. 924 826 541</p>
        </div>
    </div>
    
    <script>
        function regenerateContract() {
            if (confirm('Dette vil regenerere HTML-filen med oppdaterte data. Fortsette?')) {
                alert('Kjør følgende kommando:\n\nnode generate.js\n\nDette vil regenerere ringerike-kontrakt.html med oppdaterte data fra sample-data.json.');
            }
        }
        
        function openDataFile() {
            alert('Åpne filen "sample-data.json" i en teksteditor og oppdater:\n\n• arbeidstaker_navn\n• arbeidstaker_adresse\n• arbeidstaker_fodselsdato\n• startdato\n• stillingstittel\n• kontonummer\n• osv.\n\nKjør deretter "node generate.js" for å regenerere kontrakten.');
        }
        
        // Sjekk om HTML-filen eksisterer
        fetch('ringerike-kontrakt.html')
            .then(response => {
                if (response.ok) {
                    document.querySelector('.success').style.display = 'block';
                } else {
                    document.querySelector('.success').innerHTML = 
                        '⚠️ <strong>HTML-filen ikke funnet!</strong><br>Kjør <code>node generate.js</code> først.';
                    document.querySelector('.success').style.background = '#ffebee';
                    document.querySelector('.success').style.borderColor = '#f44336';
                    document.querySelector('.success').style.color = '#c62828';
                }
            })
            .catch(() => {
                document.querySelector('.success').innerHTML = 
                    '⚠️ <strong>HTML-filen ikke funnet!</strong><br>Kjør <code>node generate.js</code> først.';
                document.querySelector('.success').style.background = '#ffebee';
                document.querySelector('.success').style.borderColor = '#f44336';
                document.querySelector('.success').style.color = '#c62828';
            });
    </script>
</body>
</html>
