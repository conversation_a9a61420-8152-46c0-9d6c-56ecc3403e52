#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern arbeidsavtale PDF generator using WeasyPrint
Converts HTML to high-quality PDF with professional styling
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import weasyprint
        import markdown2
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("\n📦 To install required dependencies, run:")
        print("   pip install weasyprint markdown2")
        print("\n💡 Or use the npm script:")
        print("   npm run setup")
        return False

def generate_pdf(html_file='arbeidskontrakt.html', css_file='kontrakt-stil.css', output_file='arbeidskontrakt.pdf'):
    """Generate PDF from HTML and CSS files"""
    
    if not check_dependencies():
        sys.exit(1)
    
    try:
        from weasyprint import HTML, CSS
        
        # Check if input files exist
        if not os.path.exists(html_file):
            print(f"❌ HTML file not found: {html_file}")
            print("💡 Run 'node generate.js' first to generate the HTML file")
            sys.exit(1)
            
        if not os.path.exists(css_file):
            print(f"❌ CSS file not found: {css_file}")
            sys.exit(1)
        
        print(f"📄 Reading HTML file: {html_file}")
        print(f"🎨 Reading CSS file: {css_file}")
        
        # Read and process files
        html = HTML(filename=html_file)
        css = CSS(filename=css_file)
        
        print(f"🔄 Generating PDF: {output_file}")
        
        # Generate PDF with high quality settings
        html.write_pdf(
            output_file, 
            stylesheets=[css],
            optimize_images=True,
            jpeg_quality=95,
            presentational_hints=True
        )
        
        # Get file size for confirmation
        file_size = os.path.getsize(output_file)
        file_size_kb = file_size / 1024
        
        print(f"✅ PDF generated successfully!")
        print(f"📄 Output: {output_file} ({file_size_kb:.1f} KB)")
        print(f"🎯 Ready for printing or digital distribution")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating PDF: {e}")
        return False

def main():
    """Main function with command line argument handling"""
    
    # Default file names
    html_file = 'arbeidskontrakt.html'
    css_file = 'kontrakt-stil.css'
    output_file = 'arbeidskontrakt.pdf'
    
    # Simple command line argument parsing
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h']:
            print("""
🚀 Arbeidsavtale PDF Generator

Usage: python lag_pdf.py [html_file] [css_file] [output_file]

Arguments:
  html_file    HTML input file (default: arbeidskontrakt.html)
  css_file     CSS stylesheet file (default: kontrakt-stil.css)
  output_file  PDF output file (default: arbeidskontrakt.pdf)

Examples:
  python lag_pdf.py                                    # Use defaults
  python lag_pdf.py custom.html                       # Custom HTML file
  python lag_pdf.py custom.html custom.css            # Custom HTML and CSS
  python lag_pdf.py custom.html custom.css output.pdf # All custom

Dependencies:
  pip install weasyprint markdown2

Features:
  ✅ Professional PDF layout with CSS
  ✅ A4 page format with proper margins
  ✅ Page numbers and headers
  ✅ High-quality typography
  ✅ Print-ready output
            """)
            sys.exit(0)
        
        # Parse command line arguments
        if len(sys.argv) > 1:
            html_file = sys.argv[1]
        if len(sys.argv) > 2:
            css_file = sys.argv[2]
        if len(sys.argv) > 3:
            output_file = sys.argv[3]
    
    print("🚀 Starting PDF generation...")
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Generate the PDF
    success = generate_pdf(html_file, css_file, output_file)
    
    if success:
        print("\n🎉 PDF generation completed successfully!")
        print(f"📂 Open {output_file} to view your contract")
    else:
        print("\n💥 PDF generation failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
