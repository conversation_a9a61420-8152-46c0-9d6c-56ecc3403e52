# Arbeidsavtale

De fleste arbeidsavtaler hadde alt for mange feilter å fylle ut, så her er en
superenkel mal for en enkel arbeidsavtale.

```` shell
gpg --decrypt ${name}.json.gpg | \
  mustang -t arbeidskontrakt.md -f json | \
  pandoc --latex-engine=xelatex -V papersize:"a4paper" -V geometry:margin=1.0in -o kontrakt_${name}.pdf
````
```` shell
lpass show arbeidsavtale-detaljer --notes | \
  mustang -t arbeidskontrakt.md -f json | \
  pandoc --latex-engine=xelatex -V papersize:"a4paper" -V geometry:margin=1.0in -o kontrakt_${name}.pdf
````


```` json
{
    "navn" : "Foo Bar",
    "personnummer" : "12345678901",
    "adresse" : "Under en busk",
    "firma" : "FIZZBUZZ AS",
    "startdato" : "<PERSON><PERSON>r som helst",
    "stilling" : "Kodehode",
    "arbeidssted" : "I kyberrom",
    "oppsigelsesfrist" : "1 mnd",
    "lonn": "Som fortjent",
    "utbetalingsmaate" : "Under bordet",
    "utbetalingstidspunkt" : "1. hver mnd",
    "arbeidstid" : "Ja",
    "pausetid" : "Nei",
    "tariff" : "Kanskje",
    "ferie": "Nei"
}
````
