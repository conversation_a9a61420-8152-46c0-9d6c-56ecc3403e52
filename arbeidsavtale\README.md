# 🌿 Ringerike Landskap AS - Arbeidsavtale Generator

En moderne, profesjonell generator for arbeidskontrakter som bruker **HTML + CSS + WeasyPrint** for å lage høykvalitets PDF-er.

## 📋 Fullstendig eksempel inkludert

Dette repositoryet inneholder et komplett, fungerende eksempel basert på en ekte arbeidskontrakt for **Ringerike Landskap AS** med:

- ✅ Komplett arbeidskontrakt for anleggsgartner-stillinger
- ✅ Bedriftsspesifikk styling og branding
- ✅ Realistiske ansattdata (<PERSON>)
- ✅ Alle juridiske seksjoner og vilkår
- ✅ Print-klar PDF-generering

## ✨ Hvorfor denne tilnærmingen?

Denne løsningen bruker moderne webteknikker i stedet for utdaterte verktøy som LaTeX/XeLaTeX:

1. **📝 Skriv innhold i Markdown** - Enkelt og lesbart
2. **🎨 Design med CSS** - Full kontroll over layout, fonter, farger
3. **📄 Generer profesjonelle PDF-er** - <PERSON><PERSON><PERSON> kval<PERSON>t, print-klar
4. **🔧 Moderne arbeidsflyt** - Enkel å vedlikeholde og utvide

## 🛠️ Installasjon

### 1. Node.js avhengigheter
```bash
npm install
```

### 2. Python avhengigheter
```bash
pip install weasyprint markdown2
# Eller bruk:
npm run setup
```

## 🚀 Bruk

### Rask start (Ringerike Landskap AS eksempel)
```bash
# 1. Generer HTML-kontrakt for Ringerike Landskap AS
npm run generate

# 2. Åpne brukervennlig guide
start ringerike-guide.html

# 3. Eller åpne kontrakten direkte
start ringerike-kontrakt.html
```

### Alternativ: WeasyPrint (krever ekstra oppsett)
```bash
# Installer Python-avhengigheter (krever GTK på Windows)
npm run setup

# Generer PDF direkte
npm run pdf
```

### Manuell bruk
```bash
# 1. Generer HTML fra template og data
node generate.js

# 2a. Åpne i nettleser og bruk Ctrl+P for PDF
start arbeidskontrakt.html

# 2b. Eller bruk WeasyPrint (hvis installert)
python lag_pdf.py
```

## 📁 Filstruktur

### Ringerike Landskap AS eksempel (anbefalt)
- `ringerike-kontrakt.md` - Komplett arbeidskontrakt-template
- `ringerike-stil.css` - Bedriftsspesifikk styling med grønn profil
- `ringerike-guide.html` - Brukervennlig guide for PDF-generering
- `sample-data.json` - Realistiske ansattdata (Ole Hansen)

### Generelle filer
- `generate.js` - HTML-generator (Node.js)
- `lag_pdf.py` - PDF-generator (Python + WeasyPrint)
- `arbeidskontrakt.md` - Enkel template (original)
- `kontrakt-stil.css` - Generell styling

## 📋 Dataformat

Opprett en JSON-fil med kontraktdata:

```json
{
    "navn": "Ola Nordmann",
    "personnummer": "***********",
    "adresse": "Storgata 1, 0123 Oslo",
    "firma": "EKSEMPEL AS",
    "startdato": "1. januar 2025",
    "stilling": "Utvikler",
    "arbeidssted": "Oslo",
    "oppsigelsesfrist": "3 måneder",
    "lonn": "650 000 kr årlig",
    "utbetalingsmaate": "Bankoverføring",
    "utbetalingstidspunkt": "Siste arbeidsdag hver måned",
    "arbeidstid": "37,5 timer per uke",
    "pausetid": "30 minutter daglig",
    "tariff": "IT-overenskomsten",
    "ferie": "25 feriedager + 12 fridager"
}
```

## 🎨 Tilpasning

### CSS-styling
Rediger `kontrakt-stil.css` for å endre:
- Fonter og typografi
- Farger og layout
- Sidemarger og format
- Topptekst/bunntekst

### Template
Rediger `arbeidskontrakt.md` for å endre:
- Kontraktinnhold
- Seksjoner og struktur
- Mustache-variabler `{{variabel}}`

## 🔧 Avansert bruk

### Egne filer
```bash
# Bruk egne filer
node generate.js --data min-data.json
python lag_pdf.py min-kontrakt.html min-stil.css min-output.pdf
```

### Kun HTML
```bash
node generate.js  # Generer kun HTML for forhåndsvisning
```

## 📄 To metoder for PDF-generering

### Metode 1: Nettleser (Anbefalt)
1. Generer HTML: `npm run generate`
2. Åpne i nettleser: `npm run open`
3. Trykk `Ctrl+P` og velg "Lagre som PDF"

**Fordeler:**
- ✅ Fungerer på alle systemer
- ✅ Ingen ekstra avhengigheter
- ✅ Visuell kontroll før PDF-generering
- ✅ Høy kvalitet med moderne nettlesere

### Metode 2: WeasyPrint (Avansert)
1. Installer avhengigheter: `npm run setup`
2. Generer PDF: `npm run pdf`

**Fordeler:**
- ✅ Automatisert PDF-generering
- ✅ Perfekt for batch-prosessering
- ✅ Konsistent output

**Ulemper:**
- ⚠️ Krever GTK-biblioteker på Windows
- ⚠️ Mer kompleks installasjon

## ✅ Fordeler med denne løsningen

- **🎯 Profesjonell kvalitet** - Høykvalitets PDF-er
- **🎨 Full designkontroll** - CSS gir ubegrenset styling
- **📱 Moderne** - Bruker dagens beste verktøy
- **🔧 Vedlikeholdbar** - Enkel å oppdatere og utvide
- **⚡ Rask** - Effektiv generering
- **🌐 Standardbasert** - HTML/CSS er universelle standarder
- **🖥️ Universell** - Fungerer på alle operativsystemer

## 🆚 Sammenligning med gamle metoder

| Gammel metode | Ny metode |
|---------------|-----------|
| LaTeX/XeLaTeX | HTML + CSS |
| Kompleks syntax | Enkel Markdown |
| Vanskelig styling | Intuitiv CSS |
| Tunge avhengigheter | Moderne verktøy |
| Begrenset fleksibilitet | Full designkontroll |

## 📄 Eksempel output

Generatoren lager profesjonelle PDF-er med:
- A4-format med riktige marger
- Moderne typografi (Inter font)
- Sidetall og topptekst
- Strukturert layout
- Print-optimalisert kvalitet
