/* ringerike-stil.css - Profesjonell styling for Ringerike Landskap AS */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

/* Definerer selve siden */
@page {
    size: A4;
    margin: 2.5cm 2cm; /* Topp/bunn: 2.5cm, Venstre/høyre: 2cm */

    /* Definerer bunntekst med sidetall */
    @bottom-center {
        content: "Side " counter(page) " av " counter(pages);
        font-size: 9pt;
        color: #666;
        font-family: 'Inter', sans-serif;
    }
    
    /* Topptekst med bedriftsnavn */
    @top-center {
        content: "Ringerike Landskap AS - Arbeidsavtale";
        font-size: 9pt;
        color: #2d5016;
        font-family: 'Inter', sans-serif;
        border-bottom: 1px solid #4a7c59;
        padding-bottom: 5pt;
    }
}

/* Første side skal ikke ha topptekst */
@page :first {
    @top-center {
        content: "";
        border-bottom: none;
    }
}

body {
    font-family: 'Inter', sans-serif;
    font-size: 11pt;
    line-height: 1.6;
    color: #333;
    margin: 0;
    padding: 0;
}

/* Hovedoverskrift */
h1 {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 28pt;
    color: #2d5016;
    text-align: center;
    margin: 0 0 3cm 0;
    padding: 0 0 15pt 0;
    border-bottom: 3px solid #4a7c59;
    letter-spacing: 1pt;
    text-transform: uppercase;
}

/* Seksjonsoverskrifter */
h2 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 14pt;
    color: #2d5016;
    margin: 2cm 0 1cm 0;
    padding: 8pt 0 8pt 15pt;
    border-left: 4px solid #4a7c59;
    background-color: #f8faf6;
    page-break-after: avoid;
}

/* Underoverskrifter */
h3 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 12pt;
    color: #4a5568;
    margin: 1.5cm 0 0.5cm 0;
    page-break-after: avoid;
}

/* Avsnitt */
p {
    margin: 0 0 12pt 0;
    text-align: justify;
    orphans: 2;
    widows: 2;
}

/* Lister */
ul {
    margin: 0 0 12pt 0;
    padding-left: 0;
    list-style: none;
}

li {
    margin: 8pt 0;
    padding: 6pt 0;
    border-bottom: 1px solid #f1f5f9;
}

li:last-child {
    border-bottom: none;
}

/* Fremhevede elementer (navn, datoer, etc.) */
strong {
    font-weight: 600;
    color: #2d5016;
    background-color: #f0f4ed;
    padding: 2pt 6pt;
    border-radius: 3pt;
    font-size: 10.5pt;
}

/* Spesielle seksjoner */
.arbeidsgiver {
    background-color: #f8faf6;
    padding: 20pt;
    border-radius: 8pt;
    border: 2px solid #4a7c59;
    margin: 1cm 0;
    text-align: center;
}

.arbeidsgiver h2 {
    background: none;
    border: none;
    padding: 0;
    margin: 0 0 10pt 0;
    color: #2d5016;
    font-size: 18pt;
}

.arbeidstaker {
    background-color: #f8fafc;
    padding: 15pt;
    border-radius: 5pt;
    border: 1px solid #e2e8f0;
    margin: 1cm 0;
}

.oppstart {
    background-color: #fff8f0;
    padding: 15pt;
    border-radius: 5pt;
    border: 1px solid #f6ad55;
    margin: 1cm 0;
}

.lonn {
    background-color: #f0fff4;
    padding: 15pt;
    border-radius: 5pt;
    border: 1px solid #68d391;
    margin: 1cm 0;
}

/* Checkbox styling */
.checkbox {
    font-family: 'Courier New', monospace;
    font-size: 12pt;
    font-weight: bold;
    color: #2d5016;
}

/* Vilkår og betingelser */
.vilkaar {
    font-size: 10pt;
    color: #4a5568;
    font-style: italic;
    margin: 1cm 0;
    padding: 10pt;
    background-color: #f1f5f9;
    border-left: 3px solid #cbd5e0;
}

/* Signaturseksjon */
.signatures {
    margin-top: 4cm;
    page-break-inside: avoid;
    clear: both;
}

.signature-container {
    display: flex;
    justify-content: space-between;
    margin-top: 2cm;
}

.signature-block {
    width: 45%;
    text-align: center;
}

.signature-line {
    border-bottom: 2px solid #333;
    margin: 1.5cm 0 8pt 0;
    height: 1cm;
}

.signature-label {
    font-size: 10pt;
    color: #4a5568;
    font-weight: 600;
    margin-top: 5pt;
}

.date-place {
    font-size: 10pt;
    color: #4a5568;
    margin-bottom: 2cm;
    text-align: left;
}

/* Tabeller (hvis nødvendig) */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1cm 0;
    font-size: 10pt;
}

th, td {
    padding: 8pt 12pt;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

th {
    background-color: #f8faf6;
    font-weight: 600;
    color: #2d5016;
}

/* Sideskift-kontroll */
.page-break-before {
    page-break-before: always;
}

.page-break-after {
    page-break-after: always;
}

.no-break {
    page-break-inside: avoid;
}

/* Spesielle elementer */
.highlight {
    background-color: #fff8f0;
    padding: 10pt;
    border-left: 4px solid #f6ad55;
    margin: 1cm 0;
}

.important {
    background-color: #fed7d7;
    padding: 10pt;
    border-left: 4px solid #fc8181;
    margin: 1cm 0;
    font-weight: 600;
}

/* Responsiv design for mindre skjermer (forhåndsvisning) */
@media screen and (max-width: 768px) {
    body {
        font-size: 12pt;
        line-height: 1.5;
    }
    
    h1 {
        font-size: 24pt;
        margin-bottom: 2cm;
    }
    
    h2 {
        font-size: 13pt;
        margin: 1.5cm 0 0.8cm 0;
    }
    
    .signature-container {
        flex-direction: column;
    }
    
    .signature-block {
        width: 100%;
        margin-bottom: 2cm;
    }
}

/* Print-optimalisering */
@media print {
    body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .no-print {
        display: none;
    }
}
